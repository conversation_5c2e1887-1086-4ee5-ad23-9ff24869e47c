import pygame   # import the pygame library

# 1. Initialize pygame (always required before using it)
pygame.init()

# 2. Create a window (width=800, height=600)
window = pygame.display.set_mode((800, 600))

# 3. Set window title
pygame.display.set_caption("Catch the Ball")

# 4. Game loop (runs continuously until user quits)
running = True
while running:
    for event in pygame.event.get():   # check all events (like key press, quit)
        if event.type == pygame.QUIT:  # if the user clicks the X button
            running = False            # stop the loop

    # Fill the screen with black (RGB: 0,0,0)
    window.fill((0, 0, 0))

    # Update the display so changes appear
    pygame.display.update()

# 5. Quit pygame properly
pygame.quit()
