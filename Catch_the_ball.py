import pygame
import random

pygame.init()

window = pygame.display.set_mode((800, 600))
pygame.display.set_caption("Catch the Ball")

#colour codes
black = (0, 0, 0)
white = (255, 255, 255)
red = (255, 0, 0)

#load images
basket = pygame.image.load("basket.png")
ball = pygame.image.load("ball.png")

#resize images
basket = pygame.transform.scale(basket, (100, 60))
ball = pygame.transform.scale(ball, (40, 40))

#ball position
ball_x = 380
ball_y = 50
ball_speed = 5

#basket position
basket_x = 350
basket_y = 540
basket_speed = 10

#score
score = 0
font = pygame.font.Font(None, 36)

clock = pygame.time.Clock()

running = True
while running:
    for event in pygame.event.get():
        if event.type == pygame.QUIT:
            running = False
            
    #move basket
    keys = pygame.key.get_pressed()
    if keys[pygame.K_LEFT] and basket_x > 0:
        basket_x -= basket_speed
    if keys[pygame.K_RIGHT] and basket_x < 700:
        basket_x += basket_speed

    # Check collision with basket
    basket_rect = pygame.Rect(basket_x, basket_y, 100, 60)
    ball_rect = pygame.Rect(ball_x, ball_y, 40, 40)

    if basket_rect.colliderect(ball_rect):  # if basket touches ball
        score += 1
        ball_y = 0
        ball_x = random.randint(50, 750)
        
    #move ball
    ball_y += ball_speed
    if ball_y > 600:
        ball_y = 50
        ball_x = random.randint(0, 760)
        
    #draw basket and ball
    window.blit(basket, (basket_x, basket_y))
    window.blit(ball, (ball_x, ball_y))
    text = font.render("Score: " + str(score), True, white)
    window.blit(text, (10, 10))  # Draw text at (10, 10)
    
    # Update display
    pygame.display.update()

    window.fill(black)
    pygame.display.flip()
    clock.tick(60)

pygame.quit()
